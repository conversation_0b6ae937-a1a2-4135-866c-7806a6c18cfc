import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>reateDate<PERSON><PERSON><PERSON>n,
  ManyTo<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from 'typeorm';
import { BaseEntity } from '@common/base/entities/base.entity';
import { User } from '@modules/users/entities/user.entity';
import { AssignmentGroup } from '@src/modules/assignment-groups/entities/assignment-group.entity';

@Entity('group_users')
export class GroupUser extends BaseEntity {
  @Column({ type: 'varchar', length: 100 })
  role: string;

  @CreateDateColumn()
  joinedAt: Date;

  // Foreign key relationships
  @Column({ type: 'uuid' })
  groupId: string;

  @Column({ type: 'uuid' })
  userId: string;

  // Essential relationships for group management
  @ManyToOne(() => AssignmentGroup, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'group_id' })
  group: AssignmentGroup;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;
}
