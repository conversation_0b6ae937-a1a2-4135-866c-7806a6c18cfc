import { <PERSON><PERSON><PERSON>, Colum<PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { BaseEntity } from '@common/base/entities/base.entity';
import { Course } from '@modules/courses/entities/course.entity';

@Entity('assignments')
export class Assignment extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'timestamp', nullable: true })
  deadline: Date;

  // Foreign key relationships
  @Column({ type: 'uuid' })
  courseId: string;

  @Column({ type: 'uuid' })
  createdBy: string;

  // Essential relationship for course details
  @ManyToOne(() => Course, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'course_id' })
  course: Course;
}
