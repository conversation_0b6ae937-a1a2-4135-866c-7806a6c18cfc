import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON>otEmpty,
  <PERSON><PERSON><PERSON>al,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';

export class CreateThreadDto {
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(255)
  title: string;

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @IsUUID('4')
  @IsNotEmpty()
  assignmentGroupId: string;

  // TODO: for testing, need remove
  @IsUUID('4')
  userId: string;
}
