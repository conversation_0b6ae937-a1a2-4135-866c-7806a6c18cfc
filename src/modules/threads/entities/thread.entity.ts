import mongoose, { Document, Schema } from 'mongoose';

export interface IAssignmentGroup {
  id: string;
  name: string;
  description?: string;
  assignmentId: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IThread extends Document {
  title: string;
  description?: string;
  assignmentGroupId: string;
  assignmentGroup: IAssignmentGroup;
  threadUserIds: string[];
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IMessage extends Document {
  threadId: string;
  senderId: string;
  content: string;
  timestamp: Date;
}

// Thread Schema
const threadSchema = new Schema<IThread>(
  {
    title: {
      type: String,
      required: true,
      trim: true,
      maxlength: 255,
    },
    description: {
      type: String,
      trim: true,
      maxlength: 1000,
    },
    assignmentGroupId: {
      type: String,
      required: true,
    },
    assignmentGroup: {
      id: {
        type: String,
      },
      name: {
        type: String,
        trim: true,
        maxlength: 255,
      },
      description: {
        type: String,
        trim: true,
      },
      assignmentId: {
        type: String,
      },
      createdBy: {
        type: String,
      },
      createdAt: {
        type: Date,
        default: Date.now,
      },
      updatedAt: {
        type: Date,
        default: Date.now,
      },
    },
    threadUserIds: {
      type: [String],
      default: [],
    },
    createdBy: {
      type: String,
      required: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
    collection: 'threads',
  },
);

// Message Schema
const messageSchema = new Schema<IMessage>(
  {
    threadId: {
      type: String,
      required: true,
      ref: 'Thread',
    },
    senderId: {
      type: String,
      required: true,
    },
    content: {
      type: String,
      required: true,
      trim: true,
      maxlength: 2000,
    },
    timestamp: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
    collection: 'messages',
  },
);

// Indexes for better performance
threadSchema.index({ updatedAt: -1 });
messageSchema.index({ threadId: 1, timestamp: 1 });
messageSchema.index({ senderId: 1 });

// Virtual for message count
threadSchema.virtual('messageCount', {
  ref: 'Message',
  localField: '_id',
  foreignField: 'threadId',
  count: true,
});

// Ensure virtuals are serialized
threadSchema.set('toJSON', { virtuals: true });
threadSchema.set('toObject', { virtuals: true });

// Models
export const Thread = mongoose.model<IThread>('Thread', threadSchema);
export const Message = mongoose.model<IMessage>('Message', messageSchema);

// Export types for use in other files
export type ThreadDocument = IThread;
export type MessageDocument = IMessage;
