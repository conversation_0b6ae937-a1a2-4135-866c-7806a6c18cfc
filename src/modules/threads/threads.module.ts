import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ThreadsController } from './threads.controller';
import { ThreadsService } from './threads.service';
import { CustomLogger } from '@common/logging';
import { AssignmentGroup } from '@modules/assignment-groups/entities/assignment-group.entity';

@Module({
  imports: [TypeOrmModule.forFeature([AssignmentGroup])],
  controllers: [ThreadsController],
  providers: [ThreadsService, CustomLogger],
  exports: [ThreadsService],
})
export class ThreadsModule {}
