import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { BaseEntity } from '@common/base/entities/base.entity';
import { Assignment } from '@modules/assignments/entities/assignment.entity';

@Entity('assignment_groups')
export class AssignmentGroup extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  // Foreign key relationships
  @Column({ type: 'uuid' })
  createdBy: string;

  @Column({ type: 'uuid' })
  assignmentId: string;

  // Essential relationship for assignment details
  @ManyToOne(() => Assignment, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'assignment_id' })
  assignment: Assignment;
}
