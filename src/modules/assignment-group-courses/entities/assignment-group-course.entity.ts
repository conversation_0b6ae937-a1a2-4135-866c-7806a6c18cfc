import { Entity, Column } from 'typeorm';
import { BaseEntity } from '@common/base/entities/base.entity';

@Entity('assignment_group_courses')
export class AssignmentGroupCourse extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  // Foreign key relationships
  @Column({ type: 'uuid' })
  groupId: string;

  @Column({ type: 'uuid' })
  courseId: string;

  @Column({ type: 'uuid' })
  createdBy: string;
}
