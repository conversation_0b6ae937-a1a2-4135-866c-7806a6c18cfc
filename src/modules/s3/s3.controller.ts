import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { S3Service } from './s3.service';
import { PresignedUrlBaseDto } from './dto/presigned-url.dto';
import { CustomLogger } from '@common/logging';
import { JwtAuthGuard } from '@guards/jwt-auth.guard';
import { CustomResponse } from '@common/http';

@Controller('v1/s3')
@UseGuards(JwtAuthGuard)
export class S3Controller {
  private readonly loggerMeta: any;

  constructor(
    private readonly s3Service: S3Service,
    private readonly logger: CustomLogger,
  ) {
    this.loggerMeta = { context: S3Controller.name };
  }

  @Get('presigned-url')
  async generatePresignedUrl(
    @Query() query: PresignedUrlBaseDto,
  ): Promise<CustomResponse> {
    try {
      const result = await this.s3Service.generatePresignedUrl(
        query.fileExtension,
        'documents',
      );

      return result;
    } catch (error) {
      this.logger.error(
        'Failed to generate presigned URL',
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );
      throw error;
    }
  }
}
