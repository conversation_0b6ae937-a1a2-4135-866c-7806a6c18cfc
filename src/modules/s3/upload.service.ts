import {
  Injectable,
  InternalServerErrorException,
  BadRequestException,
} from '@nestjs/common';
import {
  S3Client,
  PutObjectCommand,
  DeleteObjectsCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { extname } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { ConfigService } from '@nestjs/config';
import { FileConfig } from '@configs/config.type';
import { CustomLogger } from '@common/logging';
import {
  isValidFileExtension,
  isValidMimeType,
  getMimeTypeFromExtension,
} from '@utils/file-validation.util';

@Injectable()
export class UploadService {
  private s3: S3Client;
  private bucket: string;

  private env: string;
  private region: string;
  private endpoint?: string;

  private readonly loggerMeta: any;

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: CustomLogger,
  ) {
    const fileConfig = configService.get<FileConfig>('file');
    const accessKeyId = fileConfig?.accessKeyId ?? '';
    const secretAccessKey = fileConfig?.secretAccessKey ?? '';
    this.region = fileConfig?.awsS3Region ?? '';
    this.bucket = fileConfig?.awsDefaultS3Bucket ?? '';
    this.endpoint = fileConfig?.awsS3Endpoint; // Custom endpoint

    console.log('File Config:', fileConfig);

    if (!accessKeyId || !secretAccessKey || !this.bucket) {
      throw new Error('AWS credentials are not configured');
    }

    if (!this.endpoint) {
      this.s3 = new S3Client({
        region: this.region,
        credentials: {
          accessKeyId,
          secretAccessKey,
        },
      });
    } else {
      this.s3 = new S3Client({
        endpoint: this.endpoint,
        region: this.region,
        credentials: {
          accessKeyId,
          secretAccessKey,
        },
        forcePathStyle: true, // enable if use (MinIO, Wasabi)
      });
    }
    this.env = process.env.NODE_ENV || 'development';
    this.loggerMeta = { context: UploadService.name };
  }

  async uploadFile(
    file: Express.Multer.File,
    modelName: string,
  ): Promise<string> {
    const key = `${this.env}/${modelName}/${uuidv4()}${extname(file.originalname)}`;

    console.log('Uploading file with key:', key);
    const region = await this.s3.config.region();
    console.log(region);

    const command = new PutObjectCommand({
      Bucket: this.bucket,
      Key: key,
      Body: file.buffer,
      ContentType: file.mimetype,
    });

    try {
      await this.s3.send(command);

      if (this.endpoint) {
        return `${this.endpoint}/${this.bucket}/${key}`;
      }

      return `https://${this.bucket}.s3.${this.region}.amazonaws.com/${key}`;
    } catch (error) {
      this.logger.error(
        'Failed to upload file',
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );

      throw new InternalServerErrorException('Failed to upload file');
    }
  }

  async generatePresignedUrl(
    fileExtension: string,
    modelName: string,
  ): Promise<{
    presignedUrl: string;
    key: string;
    fileUrl: string;
    fileName: string;
  }> {
    try {
      this.logger.log(
        `Generating presigned URL for file extension: ${fileExtension}, modelName: ${modelName}`,
        this.loggerMeta,
      );

      // Ensure file extension starts with dot
      const normalizedExtension = fileExtension.startsWith('.')
        ? fileExtension
        : `.${fileExtension}`;

      // Create a temporary filename for validation
      const tempFileName = `temp${normalizedExtension}`;

      // Validate file extension
      if (!isValidFileExtension(tempFileName)) {
        this.logger.warn(
          `Invalid file extension: ${fileExtension}`,
          this.loggerMeta,
        );
        throw new BadRequestException(
          'Invalid file type. Allowed types: PDF, DOCX, PPTX, XLSX, CSV, PNG, JPG, TXT',
        );
      }

      // Derive contentType from fileExtension if not provided
      const derivedContentType = getMimeTypeFromExtension(tempFileName);
      if (!derivedContentType) {
        throw new BadRequestException(
          `Unable to determine MIME type for extension: ${fileExtension}`,
        );
      }

      // Validate MIME type
      if (!isValidMimeType(derivedContentType)) {
        this.logger.warn(
          `Invalid MIME type: ${derivedContentType}`,
          this.loggerMeta,
        );
        throw new BadRequestException(
          'Invalid MIME type for the provided file',
        );
      }

      // Verify MIME type matches file extension
      const expectedMimeType = getMimeTypeFromExtension(tempFileName);
      if (expectedMimeType) {
        const expectedSubtype = expectedMimeType.split('/')[1];
        if (!derivedContentType.toLowerCase().includes(expectedSubtype)) {
          this.logger.warn(
            `MIME type mismatch: expected ${expectedMimeType}, got ${derivedContentType}`,
            this.loggerMeta,
          );
          throw new BadRequestException(
            'MIME type does not match file extension',
          );
        }
      }

      // Generate unique key with timestamp for better organization
      const timestamp = Date.now();
      const uuid = uuidv4();
      const fileName = `${timestamp}-${uuid}${normalizedExtension}`;
      const key = `${this.env}/${modelName}/${fileName}`;

      this.logger.log(`Generated S3 key: ${key}`, this.loggerMeta);

      const command = new PutObjectCommand({
        Bucket: this.bucket,
        Key: key,
        ContentType: derivedContentType,
      });

      const presignedUrl = await getSignedUrl(this.s3, command, {
        expiresIn: 900,
      });

      let fileUrl: string;
      if (this.endpoint) {
        fileUrl = `${this.endpoint}/${this.bucket}/${key}`;
      } else {
        fileUrl = `https://${this.bucket}.s3.${this.region}.amazonaws.com/${key}`;
      }

      this.logger.log(
        `Successfully generated presigned URL for key: ${key}`,
        this.loggerMeta,
      );

      return {
        presignedUrl,
        key,
        fileUrl,
        fileName,
      };
    } catch (error) {
      this.logger.error(
        `Failed to generate presigned URL for file extension: ${fileExtension}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );

      // Re-throw BadRequestException as is
      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new InternalServerErrorException(
        'Failed to generate presigned URL',
      );
    }
  }

  async deleteFiles(fileUrls: string[]): Promise<void> {
    let urlPath = '';
    if (this.endpoint) {
      urlPath = `${this.endpoint}/${this.bucket}/`;
    } else {
      urlPath = `https://${this.bucket}.s3.${this.region}.amazonaws.com/`;
    }
    const objectsToDelete = fileUrls.map((url) => {
      // Extract the key from the URL
      const key = url.replace(urlPath, '');
      return { Key: key };
    });

    const deleteParams = {
      Bucket: this.bucket,
      Delete: {
        Objects: objectsToDelete,
      },
    };

    const command = new DeleteObjectsCommand(deleteParams);

    try {
      await this.s3.send(command);
    } catch (error) {
      this.logger.error(
        'Failed to delete files from S3',
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );

      throw new InternalServerErrorException('Failed to delete files from S3');
    }
  }
}
