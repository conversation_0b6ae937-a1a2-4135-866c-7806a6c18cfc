import { Injectable } from '@nestjs/common';
import { UploadService } from '@src/modules/s3/upload.service';
import { PresignedUrlBaseResponseDto } from './dto/presigned-url.dto';
import { getMimeTypeFromExtension } from '@common/utils';
import { HTTP, CustomResponse } from '@common/http';

@Injectable()
export class S3Service extends HTTP {
  constructor(private readonly uploadService: UploadService) {
    super();
  }

  async generatePresignedUrl(
    fileExtension: string,
    modelName: string,
  ): Promise<CustomResponse> {
    const result = await this.uploadService.generatePresignedUrl(
      fileExtension,
      modelName,
    );

    // Derive contentType from the result for response
    const normalizedExtension = fileExtension.startsWith('.')
      ? fileExtension
      : `.${fileExtension}`;

    const contentType = getMimeTypeFromExtension(`temp${normalizedExtension}`);

    const responseData: PresignedUrlBaseResponseDto = {
      presignedUrl: result.presignedUrl,
      key: result.key,
      fileUrl: result.fileUrl,
      fileName: result.fileName,
      contentType: contentType || 'application/octet-stream',
    };

    return this.success(responseData);
  }
}
