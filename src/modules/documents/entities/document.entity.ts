import { <PERSON>ti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON>, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseEntity } from '@common/base/entities/base.entity';
import { User } from '@modules/users/entities/user.entity';
import { AssignmentGroup } from '@src/modules/assignment-groups/entities/assignment-group.entity';

export enum DocumentStatus {
  VISIBLE = 'visible',
  HIDDEN = 'hidden',
}

/**
 * Repository type for documents:
 * - TEMPORARY: Files uploaded via chat
 * - PERMANENT: Files uploaded via file management
 */
export enum DocumentRepositoryType {
  TEMPORARY = 'temporary',
  PERMANENT = 'permanent',
}

@Entity('documents')
export class Document extends BaseEntity {
  @Column({ type: 'varchar', length: 500 })
  fileUrl: string;

  @Column({ type: 'varchar', length: 255 })
  fileName: string;

  @Column({
    type: 'enum',
    enum: DocumentStatus,
    default: DocumentStatus.HIDDEN,
  })
  status: DocumentStatus;

  @Column({ type: 'varchar', length: 120, nullable: true })
  description: string;

  @Column({ type: 'simple-array', nullable: true })
  tags: string[];

  @Column({ type: 'varchar', length: 255, nullable: true })
  threadId: string;

  @Column({
    type: 'enum',
    enum: DocumentRepositoryType,
    default: DocumentRepositoryType.TEMPORARY,
  })
  repositoryType: DocumentRepositoryType;

  // Foreign key relationships
  @Column({ type: 'uuid' })
  uploaderId: string;

  @Column({ type: 'uuid' })
  assignmentGroupId: string;

  @Column({ type: 'uuid' })
  courseId: string;

  // Essential relationships for file management
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'uploader_id' })
  uploader: User;

  @ManyToOne(() => AssignmentGroup, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'assignment_group_id' })
  assignmentGroup: AssignmentGroup;
}
