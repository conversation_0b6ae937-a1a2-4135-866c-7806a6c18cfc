import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  <PERSON><PERSON>num,
  <PERSON><PERSON><PERSON>y,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { DocumentStatus } from '../entities/document.entity';

export class UpdateDocumentDto {
  @IsOptional()
  @IsEnum(DocumentStatus)
  status?: DocumentStatus;

  @IsOptional()
  @IsString()
  @MaxLength(120)
  description?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  // TODO: for testing, need remove
  @IsUUID('4')
  userId: string;
}
