import {
  IsOptional,
  IsString,
  IsEnum,
  IsU<PERSON>D,
  <PERSON>I<PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>Array,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import {
  DocumentStatus,
  DocumentRepositoryType,
} from '../entities/document.entity';

export enum SortField {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  FILE_NAME = 'fileName',
}

export class SearchDocumentsDto {
  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsEnum(DocumentStatus)
  f_status?: DocumentStatus;

  @IsOptional()
  @IsEnum(DocumentRepositoryType)
  f_repositoryType?: DocumentRepositoryType;

  @IsOptional()
  @IsUUID('4')
  f_assignmentGroupId?: string;

  @IsOptional()
  @IsUUID('4')
  f_courseId?: string;

  @IsOptional()
  @IsUUID('4')
  f_uploaderId?: string;

  @IsOptional()
  @IsString({ each: true })
  @Transform(({ value }) => {
    if (Array.isArray(value)) {
      return value.map((tag) => String(tag).trim());
    }
    if (typeof value === 'string') {
      return value.split(',').map((tag) => tag.trim());
    }
    return [];
  })
  f_tags?: string[];

  @IsOptional()
  @IsEnum(SortField)
  sortBy?: SortField = SortField.CREATED_AT;

  @IsOptional()
  @IsInt()
  @Min(1)
  limit?: number = 20;

  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number = 1;

  // TODO: for testing, need remove
  @IsOptional()
  @IsUUID('4')
  userId?: string;
}
