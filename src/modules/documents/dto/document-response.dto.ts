import {
  DocumentStatus,
  DocumentRepositoryType,
} from '../entities/document.entity';

export class DocumentResponseDto {
  id: string;
  fileUrl: string;
  fileName: string;
  status: DocumentStatus;
  description?: string;
  tags?: string[];
  threadId?: string;
  repositoryType: DocumentRepositoryType;
  uploaderId: string;
  assignmentGroupId: string;
  courseId: string;
  createdAt: Date;
  updatedAt: Date;
}
