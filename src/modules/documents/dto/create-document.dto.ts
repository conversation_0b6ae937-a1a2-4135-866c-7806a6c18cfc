import {
  IsString,
  <PERSON>NotEmpty,
  IsOptional,
  IsEnum,
  IsArray,
  IsUUID,
  MaxLength,
} from 'class-validator';
import { DocumentRepositoryType } from '../entities/document.entity';

export class CreateDocumentDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(500)
  fileUrl: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  fileName: string;

  @IsOptional()
  @IsString()
  @MaxLength(120)
  description?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsString()
  @MaxLength(255)
  threadId?: string;

  @IsEnum(DocumentRepositoryType)
  @IsNotEmpty()
  repositoryType: DocumentRepositoryType;

  @IsUUID('4')
  @IsNotEmpty()
  assignmentGroupId: string;

  @IsUUID('4')
  @IsNotEmpty()
  courseId: string;

  // TODO: for testing, need remove
  @IsUUID('4')
  userId: string;
}
