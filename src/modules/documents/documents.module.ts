import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DocumentsController } from './documents.controller';
import { DocumentsService } from './documents.service';
import { Document } from './entities/document.entity';
import { User } from '@modules/users/entities/user.entity';
import { AssignmentGroup } from '@modules/assignment-groups/entities/assignment-group.entity';

import { CustomLogger } from '@common/logging';

@Module({
  imports: [TypeOrmModule.forFeature([Document, User, AssignmentGroup])],
  controllers: [DocumentsController],
  providers: [DocumentsService, CustomLogger],
  exports: [DocumentsService],
})
export class DocumentsModule {}
