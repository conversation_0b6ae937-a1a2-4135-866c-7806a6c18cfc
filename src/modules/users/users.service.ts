import {
  Injectable,
  // HttpStatus,
  // HttpException,
  // NotFoundException,
  // BadRequestException,
  // Req,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
// import * as bcrypt from 'bcrypt';
import { CustomLogger } from '@src/common/logging';
// import { CreateUserDto } from './dto/create-user.dto';
import { HTTP } from '../../common/http';
// import { v4 as uuidv4 } from 'uuid';
import { MailService } from '../mail/mail.service';
// import { UpdateUserDto } from './dto/update-user.dto';
// import { SearchPagingDto } from './dto/search-paging.dto';
import {
  // Between,
  DataSource,
  // ILike,
  // In,
  // Not,
  // QueryRunner,
  Repository,
} from 'typeorm';
import { applyQueryOptions } from '@src/common/utils/query-utils';

import { I18nService } from 'nestjs-i18n';

// interface UserQueryOptions {
//   where?: any;
//   order: Record<string, 'ASC' | 'DESC'>;
//   skip: number;
//   take: number;
//   createdAt?: {
//     $gte?: Date;
//     $lte?: Date;
//   };
// }
@Injectable()
export class UsersService extends HTTP {
  private loggerMeta: any;
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly logger: CustomLogger,
    private readonly mailService: MailService,
    private readonly i18n: I18nService,
    private readonly dataSource: DataSource,
  ) {
    super();
    this.loggerMeta = { context: UsersService.name };
  }

  async getAllUsers(
    filters: Record<string, string>,
    searchValue?: string,
    sortField?: string,
    page: number = 1,
    limit: number = 50,
  ): Promise<any> {
    try {
      this.logger.log(
        `Fetching users with filters: ${JSON.stringify(filters)}`,
        this.loggerMeta,
      );

      let queryBuilder = this.userRepository
        .createQueryBuilder('user')
        .where({ isAdmin: false });

      queryBuilder = applyQueryOptions(
        queryBuilder,
        filters,
        ['name', 'email'],
        searchValue,
        sortField,
        page,
        limit,
      );

      const [data, total] = await queryBuilder.getManyAndCount();

      this.logger.log(`Fetched ${data.length} users`, this.loggerMeta);
      return this.success({
        data: data ?? [],
        total: total ?? 0,
        page: page,
        limit: limit,
        totalPages: Math.ceil(total / limit),
      });
    } catch (error) {
      this.logger.error(
        `Error fetching users: ${error}`,
        this.loggerMeta,
        error.stack,
      );
      return this.setHttpRequest(500, 'Internal server error');
    }
  }

  async findAllAdmin(sortField?: string, page: number = 1, limit: number = 50) {
    try {
      this.logger.log(
        `Fetching users with filters: ${JSON.stringify(sortField)}`,
        this.loggerMeta,
      );

      const [data, count] = await this.userRepository.findAndCount({
        where: { isAdmin: true },
        relations: ['roles', 'department'],
        skip: (page - 1) * limit,
        take: limit,
      });

      if (!data.length) {
        this.logger.debug(`No users found`, this.loggerMeta);
        return this.setHttpRequest(404, 'No users found');
      }

      this.logger.log(`Fetched ${data.length} users`, this.loggerMeta);
      return this.success({ data, count });
    } catch (error) {
      console.log(error);
      return this.setHttpRequest(400, 'Bad request');
    }
  }
  async findByUsername(username: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { email: username } });
  }

  async findByAdminUsername(username: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { email: username, isAdmin: true },
    });
  }

  async findById(id: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { id } });
  }

  async createUser(createUserDto: any): Promise<User> {
    const user = this.userRepository.create(createUserDto as Partial<User>);
    return await this.userRepository.save(user);
  }
}
