import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  BeforeInsert,
  ManyToMany,
  JoinTable,
  <PERSON>inColumn,
  DeleteDateColumn,
  Index,
} from 'typeorm';
import * as bcrypt from 'bcrypt';
import { BaseEntity } from '@src/common/base/entities/base.entity';

@Entity('users')
export class User extends BaseEntity {
  @Index('idx_user_name')
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Index('idx_user_email', { unique: true })
  @Column({ type: 'varchar', length: 255, unique: true, nullable: false })
  email: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  phoneNumber: string;

  @Column({ type: 'boolean' })
  isAdmin: boolean;

  @Column({ type: 'boolean' })
  isInstructor: boolean;

  @Column({ type: 'varchar', length: 255, nullable: true })
  password: string;
}
