import {
  IsEmail,
  IsEnum,
  <PERSON>NotEmpty,
  IsOptional,
  IsString,
  Length,
  Matches,
} from 'class-validator';
import { i18nValidationMessage } from 'nestjs-i18n';

enum Gender {
  MALE = 'Male',
  FEMALE = 'Female',
}

export class CreateUserDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsEnum(Gender)
  @IsNotEmpty()
  gender: Gender;

  @IsEmail({}, { message: i18nValidationMessage('common.validation.isEmail') })
  @IsNotEmpty()
  email: string;

  @IsOptional()
  @IsString()
  @Matches(/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/, {
    message: i18nValidationMessage('common.validation.isPassword'),
  })
  password: string;
}
