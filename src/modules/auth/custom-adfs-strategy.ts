import { Strategy as PassportStrategy } from 'passport';
import { Request } from 'express';
import * as https from 'https';
import * as querystring from 'querystring';
import * as url from 'url';
// TODO: Replace with your own config import or process.env
// import { adfsConfig } from './config';

function extractString(val: any): string | undefined {
  if (typeof val === 'string') return val;
  if (Array.isArray(val) && typeof val[0] === 'string') return val[0];
  return undefined;
}

interface AdfsStrategyOptions {
  authorizationURL: string;
  tokenURL: string;
  clientID: string;
  clientSecret?: string;
  callbackURL: string;
  scope?: string;
  state?: boolean;
  passReqToCallback?: boolean;
  resource?: string;
}

export interface UserProfile {
  provider: string;
  id: string;
  displayName: string;
  name: {
    familyName: string;
    givenName: string;
  };
  emails: Array<{ value: string }>;
  upn?: string;
  winaccountname?: string;
  apptype?: string;
  appid?: string;
  authmethod?: string;
  auth_time?: number;
  version?: string;
  accessToken: string;
  expiresAt: number;
  refreshToken?: string;
}

export class CustomAdfsStrategy extends PassportStrategy {
  private _verify: (
    accessToken: string,
    refreshToken: string,
    profile: UserProfile,
    done: any,
  ) => void;
  private _options: AdfsStrategyOptions;
  private _oauth2: any;

  constructor(
    options: AdfsStrategyOptions,
    verify: (
      accessToken: string,
      refreshToken: string,
      profile: UserProfile,
      done: any,
    ) => void,
  ) {
    super();
    if (!verify) {
      throw new TypeError('CustomAdfsStrategy requires a verify callback');
    }
    if (!options.authorizationURL) {
      throw new TypeError(
        'CustomAdfsStrategy requires an authorizationURL option',
      );
    }
    if (!options.tokenURL) {
      throw new TypeError('CustomAdfsStrategy requires a tokenURL option');
    }
    if (!options.clientID) {
      throw new TypeError('CustomAdfsStrategy requires a clientID option');
    }
    this.name = 'adfs';
    this._verify = verify;
    this._options = {
      ...options,
      clientSecret: options.clientSecret || 'empty-secret',
    };
  }

  authenticate(req: Request, options: any = {}) {
    const self = this;
    if (req.query && req.query.error) {
      if (req.query.error === 'access_denied') {
        return this.fail({
          message:
            extractString(req.query.error_description) || 'Access denied',
        });
      } else {
        return this.error(
          new Error(
            `OAuth error: ${req.query.error} - ${req.query.error_description}`,
          ),
        );
      }
    }
    if (req.query && req.query.code) {
      const codeParam = req.query.code;
      const code = Array.isArray(codeParam) ? codeParam[0] : codeParam;
      if (typeof code !== 'string') {
        return this.error(new Error('Invalid authorization code'));
      }
      const callbackURL =
        extractString(options.callbackURL) || this._options.callbackURL;
      if (typeof callbackURL !== 'string') {
        return this.error(new Error('Invalid callback URL'));
      }
      this._getAccessToken(
        code,
        callbackURL,
        (err: any, accessToken?: string, refreshToken?: string) => {
          if (err) {
            return self.error(err);
          }
          if (!accessToken) {
            return self.error(new Error('No access token received'));
          }
          self.userProfile(accessToken, (err: any, profile?: UserProfile) => {
            if (err) {
              return self.error(err);
            }
            if (!profile) {
              return self.error(new Error('Failed to parse user profile'));
            }
            if (refreshToken) {
              profile.refreshToken = refreshToken;
            }
            function verified(err: any, user: any, info: any) {
              if (err) {
                return self.error(err);
              }
              if (!user) {
                return self.fail(info);
              }
              self.success(user, info);
            }
            try {
              self._verify(accessToken, refreshToken || '', profile, verified);
            } catch (ex) {
              return self.error(ex);
            }
          });
        },
      );
    } else {
      const callbackURL =
        extractString(options.callbackURL) || this._options.callbackURL;
      if (typeof callbackURL !== 'string') {
        return this.error(new Error('Invalid callback URL'));
      }
      const params: { [key: string]: string } =
        this.authorizationParams(options);
      params.response_type = 'code';
      params.redirect_uri = callbackURL;
      params.client_id = this._options.clientID;
      const scope = options.scope || this._options.scope;
      if (scope) {
        params.scope = scope;
      }
      const authorizationURL = new URL(this._options.authorizationURL);
      Object.keys(params).forEach((key) => {
        authorizationURL.searchParams.set(key, params[key]);
      });
      this.redirect(authorizationURL.toString());
    }
  }

  private _getAccessToken(
    code: string,
    callbackURL: string,
    done: (err: any, accessToken?: string, refreshToken?: string) => void,
  ) {
    if (typeof callbackURL !== 'string') {
      return done(new Error('Invalid callback URL type'));
    }
    const params: any = {
      grant_type: 'authorization_code',
      code: code,
      redirect_uri: callbackURL,
      client_id: this._options.clientID,
      resource: this._options.resource || process.env.ADFS_AUDIENCE,
    };
    if (
      this._options.clientSecret &&
      this._options.clientSecret !== 'empty-secret' &&
      this._options.clientSecret !== ''
    ) {
      params.client_secret = this._options.clientSecret;
    }
    const postData = querystring.stringify(params);
    const parsedURL = url.parse(this._options.tokenURL);
    const options = {
      hostname: parsedURL.hostname,
      port: parsedURL.port || 443,
      path: parsedURL.path,
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': Buffer.byteLength(postData),
      },
    };
    const req = https.request(options, (res: any) => {
      let data = '';
      res.on('data', (chunk: any) => {
        data += chunk;
      });
      res.on('end', () => {
        if (res.statusCode === 200) {
          try {
            const response = JSON.parse(data);
            if (response.access_token) {
              return done(null, response.access_token, response.refresh_token);
            } else {
              return done(new Error('No access_token in response'));
            }
          } catch (e) {
            return done(new Error('Invalid token response format'));
          }
        } else {
          return done(
            new Error(`Token endpoint error: ${res.statusCode} - ${data}`),
          );
        }
      });
    });
    req.on('error', (err: any) => {
      return done(err);
    });
    req.write(postData);
    req.end();
  }

  userProfile(
    accessToken: string,
    done: (err: any, profile?: UserProfile) => void,
  ) {
    try {
      const tokenParts = accessToken.split('.');
      if (tokenParts.length !== 3) {
        return done(new Error('Invalid JWT token format'));
      }
      const payload = JSON.parse(
        Buffer.from(tokenParts[1], 'base64').toString(),
      );
      const profile: UserProfile = {
        provider: 'adfs',
        id: payload.sub || payload.oid || payload.upn,
        displayName: payload.given_name || payload.name || payload.unique_name,
        name: {
          familyName: payload.family_name || '',
          givenName: payload.given_name || '',
        },
        emails: payload.email ? [{ value: payload.email }] : [],
        upn: payload.upn,
        winaccountname: payload.winaccountname,
        apptype: payload.apptype,
        appid: payload.appid,
        authmethod: payload.authmethod,
        auth_time: payload.auth_time,
        version: payload.ver,
        accessToken: accessToken,
        expiresAt: payload.exp * 1000,
      };
      return done(null, profile);
    } catch (e) {
      return done(e);
    }
  }

  refreshAccessToken(
    refreshToken: string,
    done: (err: any, accessToken?: string, newRefreshToken?: string) => void,
  ) {
    const params: any = {
      grant_type: 'refresh_token',
      refresh_token: refreshToken,
      client_id: this._options.clientID,
      resource: this._options.resource || process.env.ADFS_AUDIENCE,
    };
    if (
      this._options.clientSecret &&
      this._options.clientSecret !== 'empty-secret' &&
      this._options.clientSecret !== ''
    ) {
      params.client_secret = this._options.clientSecret;
    }
    const postData = querystring.stringify(params);
    const parsedURL = url.parse(this._options.tokenURL);
    const options = {
      hostname: parsedURL.hostname,
      port: parsedURL.port || 443,
      path: parsedURL.path,
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': Buffer.byteLength(postData),
      },
    };
    const req = https.request(options, (res: any) => {
      let data = '';
      res.on('data', (chunk: any) => {
        data += chunk;
      });
      res.on('end', () => {
        if (res.statusCode === 200) {
          try {
            const response = JSON.parse(data);
            if (response.access_token) {
              return done(null, response.access_token, response.refresh_token);
            } else {
              return done(new Error('No access_token in response'));
            }
          } catch (e) {
            return done(new Error('Invalid response format'));
          }
        } else {
          return done(new Error(`ADFS error: ${res.statusCode} - ${data}`));
        }
      });
    });
    req.on('error', (err: any) => {
      return done(err);
    });
    req.write(postData);
    req.end();
  }

  revokeRefreshToken(refreshToken: string, done: (err: any) => void) {
    // No standard endpoint for ADFS token revocation; implement local blacklist if needed
    return done(null);
  }

  authorizationParams(options: any) {
    const params: any = {};
    params.resource = this._options.resource || process.env.ADFS_AUDIENCE;
    params.scope = 'openid profile email offline_access';
    return params;
  }

  tokenParams(options: any) {
    const params: any = {};
    params.resource = this._options.resource || process.env.ADFS_AUDIENCE;
    return params;
  }
}
