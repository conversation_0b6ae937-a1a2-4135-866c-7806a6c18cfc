import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
// import { jwtConstants } from './constants';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: 'airport-connect-default-secret-key',
    });
  }

  async validate(payload: any) {
    return {
      email: payload.email,
      userId: payload.userId,
      isAdmin: payload.isAdmin,
      isInstructor: payload.isInstructor,
    };
  }
}
