import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateUsersTable1739174391665 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Install unaccent extension for text search functionality
    await queryRunner.query('CREATE EXTENSION IF NOT EXISTS unaccent;');
    await queryRunner.createTable(
      new Table({
        name: 'users',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
          },
          {
            name: 'name',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'gender',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'phone_number',
            type: 'varchar',
            // isUnique: true,
            isNullable: true,
          },
          {
            name: 'email',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'is_admin',
            type: 'boolean',
            default: false,
            isNullable: false,
          },
          {
            name: 'is_instructor',
            type: 'boolean',
            isNullable: false,
            default: false,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('users');
    // Drop unaccent extension (only if no other objects depend on it)
    await queryRunner.query('DROP EXTENSION IF EXISTS unaccent;');
  }
}
