import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddPasswordColumnToUsersTable1753843087550
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'users',
      new TableColumn({
        name: 'password',
        type: 'varchar',
        isNullable: true, // Allow null for existing users
        length: '255', // Adjust length as needed
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('users', 'password');
  }
}
