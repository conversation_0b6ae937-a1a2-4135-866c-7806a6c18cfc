import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from '@modules/users/entities/user.entity';
import { Course } from '@modules/courses/entities/course.entity';
import { AssignmentGroup } from '@src/modules/assignment-groups/entities/assignment-group.entity';
import { AssignmentGroupCourse } from '@modules/assignment-group-courses/entities/assignment-group-course.entity';

export async function seedGroupAssignmentCourses(
  dataSource: DataSource,
  users: User[],
  courses: Course[],
  assignmentGroups: AssignmentGroup[],
): Promise<AssignmentGroupCourse[]> {
  const groupAssignmentCourseRepository = dataSource.getRepository(
    AssignmentGroupCourse,
  );

  // Check if data already exists
  const existingCount = await groupAssignmentCourseRepository.count();
  if (existingCount > 0) {
    console.log('⏭️  Group assignment courses already exist, skipping seed');
    return await groupAssignmentCourseRepository.find();
  }

  // Use passed data
  const instructor = users.find((u) => u.name === 'Dr. <PERSON>')!;

  const groupAssignmentCourses = [
    {
      id: uuidv4(),
      name: 'Software Engineering Project Course',
      description:
        'Advanced course focusing on real-world software development practices and methodologies.',
      groupId: assignmentGroups[0].id,
      courseId: courses[0].id,
      createdBy: instructor.id,
    },
    {
      id: uuidv4(),
      name: 'Database Systems Project Course',
      description:
        'Practical database design and implementation course with hands-on projects.',
      groupId: assignmentGroups[1].id,
      courseId: courses[1].id,
      createdBy: instructor.id,
    },
  ];

  const savedGroupAssignmentCourses =
    await groupAssignmentCourseRepository.save(groupAssignmentCourses);
  console.log(
    `✅ Created ${savedGroupAssignmentCourses.length} group assignment courses`,
  );
  return savedGroupAssignmentCourses;
}
