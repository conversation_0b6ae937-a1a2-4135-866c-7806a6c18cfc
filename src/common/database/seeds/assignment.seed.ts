import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from '@modules/users/entities/user.entity';
import { Course } from '@modules/courses/entities/course.entity';
import { Assignment } from '@modules/assignments/entities/assignment.entity';

export async function seedAssignments(
  dataSource: DataSource,
  users: User[],
  courses: Course[],
): Promise<Assignment[]> {
  console.log('🌱 Seeding assignments...');
  const assignmentRepository = dataSource.getRepository(Assignment);

  // Check if assignments already exist
  const existingCount = await assignmentRepository.count();
  if (existingCount > 0) {
    console.log('✅ Assignments already exist, skipping assignment seeding');
    return await assignmentRepository.find();
  }

  // Find instructor
  const instructor = users.find((u) => u.isInstructor && u.name === 'Dr. John');
  if (!instructor) {
    throw new Error('Instructor not found for assignment seeding');
  }

  const assignments = [
    {
      id: uuidv4(),
      name: 'Final Project - E-commerce Platform',
      description:
        'Build a full-stack e-commerce platform with modern technologies including React, Node.js, and PostgreSQL. Focus on scalability, security, and user experience.',
      deadline: new Date('2024-12-15T23:59:59Z'),
      courseId: courses[0].id,
      createdBy: instructor.id,
    },
    {
      id: uuidv4(),
      name: 'Database Design Project - Library Management',
      description:
        'Design and implement a comprehensive library management system database. Include proper normalization, indexing strategies, and complex queries.',
      deadline: new Date('2024-11-30T23:59:59Z'),
      courseId: courses[1].id,
      createdBy: instructor.id,
    },
    {
      id: uuidv4(),
      name: 'ML Model Development - Predictive Analytics',
      description:
        'Develop and train machine learning models for predictive analytics. Implement data preprocessing, feature engineering, model selection, and evaluation metrics.',
      deadline: new Date('2024-12-20T23:59:59Z'),
      courseId: courses[2].id,
      createdBy: instructor.id,
    },
  ];

  const savedAssignments = await assignmentRepository.save(assignments);
  console.log(`✅ Created ${savedAssignments.length} assignments`);
  return savedAssignments;
}
