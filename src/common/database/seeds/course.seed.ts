import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { Course } from '@modules/courses/entities/course.entity';

export async function seedCourses(dataSource: DataSource): Promise<Course[]> {
  console.log('🌱 Seeding courses...');
  const courseRepository = dataSource.getRepository(Course);

  // Check if courses already exist
  const existingCourses = await courseRepository.find();
  if (existingCourses.length > 0) {
    console.log('✅ Courses already exist, skipping course seeding');
    return existingCourses;
  }

  const courses = [
    {
      id: uuidv4(),
      name: 'Advanced Software Engineering',
    },
    {
      id: uuidv4(),
      name: 'Database Systems and Design',
    },
    {
      id: uuidv4(),
      name: 'Machine Learning Fundamentals',
    },
  ];

  const savedCourses = await courseRepository.save(courses);
  console.log(`✅ Created ${savedCourses.length} courses`);
  return savedCourses;
}
