import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from '@modules/users/entities/user.entity';
import { Course } from '@modules/courses/entities/course.entity';
import { CourseUser } from '@modules/course-users/entities/course-user.entity';

export async function seedCourseUsers(
  dataSource: DataSource,
  users: User[],
  courses: Course[],
): Promise<CourseUser[]> {
  console.log('🌱 Seeding course-user relationships...');
  const courseUserRepository = dataSource.getRepository(CourseUser);

  // Check if course users already exist
  const existingCourseUsers = await courseUserRepository.find();
  if (existingCourseUsers.length > 0) {
    console.log('✅ Course users already exist, skipping course user seeding');
    return existingCourseUsers;
  }

  const instructor1 = users.find((u) => u.name === 'Dr. John')!;
  const student1 = users.find((u) => u.name === 'Alex')!;
  const student2 = users.find((u) => u.name === 'Watson')!;

  const courseUsers = [
    {
      id: uuidv4(),
      name: 'Instructor01',
      courseId: courses[0].id,
      userId: instructor1.id,
    },
    {
      id: uuidv4(),
      name: 'Instructor01',
      courseId: courses[1].id,
      userId: instructor1.id,
    },
    {
      id: uuidv4(),
      name: 'Instructor01',
      courseId: courses[2].id,
      userId: instructor1.id,
    },
    {
      id: uuidv4(),
      name: 'Student01',
      courseId: courses[0].id,
      userId: student1.id,
    },
    {
      id: uuidv4(),
      name: 'Student01',
      courseId: courses[1].id,
      userId: student1.id,
    },
    {
      id: uuidv4(),
      name: 'Student01',
      courseId: courses[2].id,
      userId: student1.id,
    },
    {
      id: uuidv4(),
      name: 'Student02',
      courseId: courses[0].id,
      userId: student2.id,
    },
    {
      id: uuidv4(),
      name: 'Student02',
      courseId: courses[1].id,
      userId: student2.id,
    },
    {
      id: uuidv4(),
      name: 'Student02',
      courseId: courses[2].id,
      userId: student2.id,
    },
  ];

  const savedCourseUsers = await courseUserRepository.save(courseUsers);
  console.log(
    `✅ Created ${savedCourseUsers.length} course-user relationships`,
  );
  return savedCourseUsers;
}
