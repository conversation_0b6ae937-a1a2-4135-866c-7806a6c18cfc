import { AppDataSource } from '@database/data-source';
import { User } from '@modules/users/entities/user.entity';
import * as bcrypt from 'bcrypt';

async function adminSeed() {
  try {
    await AppDataSource.initialize();
    const repo = AppDataSource.getRepository(User);
    const email = '<EMAIL>';
    const password = 'Vinova@123';
    const isAdmin = true;
    const isInstructor = false;

    const existing = await repo.findOneBy({ email: email });

    if (!existing) {
      const hashedPassword = await bcrypt.hash(password, 10);

      const admin = repo.create({
        email: email,
        phoneNumber: '+6581234567',
        isAdmin: isAdmin,
        isInstructor: isInstructor,
        password: hashedPassword,
      });

      await repo.save(admin);
      console.log('Super admin seed completed successfully.');
    } else {
      console.log('Super admin already exists, skipping seed.');
      process.exit(1);
    }
  } catch (err) {
    console.error('Error while running super admin seed:', err);
    process.exit(1);
  }
}

adminSeed().catch((err) => {
  console.error(err);
  process.exit(1);
});
