import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from '@modules/users/entities/user.entity';
import { Assignment } from '@modules/assignments/entities/assignment.entity';
import { AssignmentGroup } from '@src/modules/assignment-groups/entities/assignment-group.entity';

export async function seedAssignmentGroups(
  dataSource: DataSource,
  users: User[],
  assignments: Assignment[],
): Promise<AssignmentGroup[]> {
  const groupAssignmentRepository = dataSource.getRepository(AssignmentGroup);

  console.log('🌱 Seeding assignment groups...');

  // Check if data already exists
  const existingCount = await groupAssignmentRepository.count();
  if (existingCount > 0) {
    console.log(
      '✅ Assignment groups already exist, skipping assignment group seeding',
    );
    return await groupAssignmentRepository.find({
      relations: ['assignment'],
    });
  }

  // Find instructor
  const instructor = users.find((u) => u.isInstructor && u.name === 'Dr. <PERSON>');
  if (!instructor) {
    throw new Error('Instructor not found for assignment group seeding');
  }

  const assignmentGroups = [
    {
      id: uuidv4(),
      name: 'Team Alpha - E-commerce Platform',
      description:
        'Group working on the e-commerce platform project. Focus on frontend development and user experience.',
      createdBy: instructor.id,
      assignmentId: assignments[0].id,
    },
    {
      id: uuidv4(),
      name: 'Team Beta - E-commerce Platform',
      description:
        'Group working on the e-commerce platform project. Focus on backend development and database design.',
      createdBy: instructor.id,
      assignmentId: assignments[0].id,
    },
    {
      id: uuidv4(),
      name: 'Team Gamma - Library Management',
      description:
        'Group working on the library management system. Focus on database normalization and query optimization.',
      createdBy: instructor.id,
      assignmentId: assignments[1].id,
    },
    {
      id: uuidv4(),
      name: 'Team Delta - ML Analytics',
      description:
        'Group working on machine learning predictive analytics. Focus on data preprocessing and model training.',
      createdBy: instructor.id,
      assignmentId: assignments[2].id,
    },
  ];

  const savedAssignmentGroups =
    await groupAssignmentRepository.save(assignmentGroups);
  console.log(`✅ Created ${savedAssignmentGroups.length} assignment groups`);

  // Return assignment groups with assignment relationship loaded
  return await groupAssignmentRepository.find({
    relations: ['assignment'],
  });
}
