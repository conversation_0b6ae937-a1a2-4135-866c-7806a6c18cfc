import { AppDataSource } from '@database/data-source';
import { User } from '@modules/users/entities/user.entity';
import { AssignmentGroup } from '@src/modules/assignment-groups/entities/assignment-group.entity';

/**
 * Fetches actual data from PostgreSQL database
 * This ensures MongoDB documents reference real entities
 */
export async function fetchPostgreSQLData() {
  console.log('📊 Fetching data from PostgreSQL...');

  if (!AppDataSource.isInitialized) {
    await AppDataSource.initialize();
  }

  const userRepository = AppDataSource.getRepository(User);
  const groupAssignmentRepository =
    AppDataSource.getRepository(AssignmentGroup);

  const users = await userRepository.find();
  const groupAssignments = await groupAssignmentRepository.find({
    relations: ['assignment'],
  });

  console.log(
    `✅ Fetched ${users.length} users and ${groupAssignments.length} group assignments`,
  );

  return { users, groupAssignments };
}
