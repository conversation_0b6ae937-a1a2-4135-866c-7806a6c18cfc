#!/usr/bin/env ts-node

import mongoose from 'mongoose';
import { AppDataSource } from '@database/data-source';
import { fetchPostgreSQLData } from './data-fetcher';
import { seedThreads } from './thread.seed';
import { seedMessages } from './message.seed';

async function runMongoSeeds(standalone: boolean = true) {
  if (standalone) {
    console.log('🚀 Starting MongoDB seeding...');
  }

  try {
    // Connect to MongoDB
    const mongoUri =
      process.env.MONGODB_URI ||
      '*************************************************************************';
    if (
      mongoose.connection.readyState !== mongoose.ConnectionStates.connected
    ) {
      await mongoose.connect(mongoUri);
    }
    console.log('✅ MongoDB connection established');

    // Fetch data from PostgreSQL
    const { users, groupAssignments } = await fetchPostgreSQLData();

    // Seed data in the correct order
    const threads = await seedThreads(users, groupAssignments);
    const messages = await seedMessages(threads, users);

    console.log('\n📊 MongoDB Summary:');
    console.log(`   💬 Threads: ${threads.length}`);
    console.log(`   📝 Messages: ${messages.length}`);

    // Only close MongoDB connection if running standalone
    if (standalone) {
      await mongoose.connection.close();
      console.log('✅ MongoDB connection closed');
    } else {
      console.log('✅ MongoDB connection kept open for document seeding');
    }

    // Only close PostgreSQL connection and exit if running standalone
    if (standalone) {
      if (AppDataSource.isInitialized) {
        await AppDataSource.destroy();
        console.log('✅ PostgreSQL connection closed');
      }

      console.log('\n🎉 MongoDB seeding completed successfully!');
      process.exit(0);
    }
  } catch (err) {
    console.error('❌ Error while running MongoDB seeders:', err);
    if (standalone) {
      process.exit(1);
    } else {
      throw err; // Re-throw error for parent process to handle
    }
  }
}

// Run if this file is executed directly
if (require.main === module) {
  void runMongoSeeds(true); // Run in standalone mode
}

export { runMongoSeeds };
