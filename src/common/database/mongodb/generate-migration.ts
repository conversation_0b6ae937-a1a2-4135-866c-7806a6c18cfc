import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';

function getNextVersion(migrationsPath: string): string {
  if (!fs.existsSync(migrationsPath)) return '001';
  const files = fs.readdirSync(migrationsPath);
  const versions = files
    .map((f) => {
      const match = f.match(/(\d{3})-.*\.migration\.ts$/);
      return match ? parseInt(match[1], 10) : null;
    })
    .filter((v): v is number => v !== null);
  const next = (Math.max(...versions, 0) + 1).toString().padStart(3, '0');
  return next;
}

function generateMigrationTemplate(className: string, version: string): string {
  return `import { Connection } from 'mongoose';
import { IMigration } from '../migration.interface';

export class ${className} implements IMigration {
  name = '${className}';
  version = '${version}';

  async up(connection: Connection): Promise<void> {
    const db = connection.db!;

    // TODO: Add your migration logic here
    // Example:
    // await db.createCollection('new_collection');
    // const collection = db.collection('existing_collection');
    // await collection.createIndex({ field: 1 });

    console.log('✓ Migration completed: ${className}');
  }

  async down(connection: Connection): Promise<void> {
    const db = connection.db!;

    // TODO: Add your rollback logic here
    // Example:
    // await db.dropCollection('new_collection');

    console.log('✓ Rollback completed: ${className}');
  }
}
`;
}

function updateMigrationService(className: string, fileName: string): void {
  const migrationServicePath = path.resolve(__dirname, 'migration.service.ts');

  if (!fs.existsSync(migrationServicePath)) {
    console.error(`Migration service file not found: ${migrationServicePath}`);
    process.exit(1);
  }

  let content = fs.readFileSync(migrationServicePath, 'utf8');

  // Add import statement after the last import
  const importStatement = `import { ${className} } from './migrations/${fileName.replace('.ts', '')}';`;
  const lastImportMatch = content.match(/^import.*from.*;\s*$/gm);

  if (lastImportMatch) {
    const lastImport = lastImportMatch[lastImportMatch.length - 1];
    const lastImportIndex = content.lastIndexOf(lastImport);
    const insertIndex = lastImportIndex + lastImport.length;
    content =
      content.slice(0, insertIndex) +
      importStatement +
      '\n' +
      content.slice(insertIndex);
  } else {
    // If no imports found, add after the first line
    const lines = content.split('\n');
    lines.splice(1, 0, importStatement);
    content = lines.join('\n');
  }

  // Add registration call in registerMigrations method
  const registrationCall = `    this.migrationRunner.registerMigration(new ${className}());`;

  // Find the registerMigrations method and add the registration call before the closing brace
  const registerMethodRegex =
    /(private registerMigrations\(\): void \{[\s\S]*?)(\n\s*\/\/ Add more migrations here as needed[\s\S]*?)(\n\s*\})/;
  const match = content.match(registerMethodRegex);

  if (match) {
    // Insert the registration call before the comment line
    const beforeComment = match[1];
    const commentAndAfter = match[2];
    const closingBrace = match[3];

    content = content.replace(
      registerMethodRegex,
      beforeComment + '\n' + registrationCall + commentAndAfter + closingBrace,
    );
  } else {
    console.error(
      'Could not find registerMigrations method in migration.service.ts',
    );
    process.exit(1);
  }

  fs.writeFileSync(migrationServicePath, content);
  console.log('✓ Updated migration.service.ts with new migration registration');
}

function main() {
  const migrationName = process.argv[2];

  if (!migrationName) {
    console.error('Usage: ts-node generate-migration.ts <migration-name>');
    console.error('Example: ts-node generate-migration.ts add-user-indexes');
    process.exit(1);
  }

  const migrationsPath = path.resolve(__dirname, 'migrations');
  if (!fs.existsSync(migrationsPath)) {
    fs.mkdirSync(migrationsPath, { recursive: true });
  }

  const version = getNextVersion(migrationsPath);
  const className =
    migrationName
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join('') + 'Migration';

  const fileName = `${version}-${migrationName}.migration.ts`;
  const filePath = path.join(migrationsPath, fileName);

  const template = generateMigrationTemplate(className, version);

  fs.writeFileSync(filePath, template);

  console.log(`[mongodb:migration:generate] Created: ${fileName}`);
  console.log(`Class name: ${className}`);
  console.log(`File path: ${filePath}`);

  // Automatically register the migration
  updateMigrationService(className, fileName);

  console.log('✓ Migration automatically registered in migration.service.ts');
  console.log('');
  console.log('Next steps:');
  console.log('Edit the migration file to add your logic');
}

main();
