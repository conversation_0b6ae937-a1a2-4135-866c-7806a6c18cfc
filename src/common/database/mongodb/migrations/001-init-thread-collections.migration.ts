import { Connection } from 'mongoose';
import { IMigration } from '../migration.interface';

export class InitThreadCollectionsMigration implements IMigration {
  name = 'InitThreadCollections';
  version = '001';

  async up(connection: Connection): Promise<void> {
    const db = connection.db!;

    // Create threads collection
    await db.createCollection('threads');
    console.log('✓ Created threads collection');

    // Create messages collection
    await db.createCollection('messages');
    console.log('✓ Created messages collection');

    // Note: Indexes are automatically created by Mongoose based on schema definitions
    console.log('✓ Indexes will be created automatically by Mongoose schema');
  }

  async down(connection: Connection): Promise<void> {
    const db = connection.db!;

    // Drop collections
    await db.dropCollection('messages');
    console.log('✓ Dropped messages collection');

    await db.dropCollection('threads');
    console.log('✓ Dropped threads collection');
  }
}
