import {
  Injectable,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    const path = request.url;

    // Public routes that don't require authentication
    const publicRoutes = [
      '/admin/auth/login',
      '/oauth2/login',
      '/oauth2/callback',
      '/v1/s3/presigned-url', //TODO: Remove this after having jwt
      '/v1/documents', // Remove this after having jwt
      '/v1/threads', // Remove this after having jwt
    ];

    if (publicRoutes.some((route) => path.includes(route))) {
      return true;
    }

    return super.canActivate(context) as boolean;
  }

  handleRequest(err: any, user: any, info: any): any {
    if (err || !user) {
      throw new UnauthorizedException('Invalid or missing token');
    }
    return user;
  }
}
