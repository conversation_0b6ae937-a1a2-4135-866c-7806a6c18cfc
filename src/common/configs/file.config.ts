import { registerAs } from '@nestjs/config';
import { FileConfig } from './config.type';
import {
  // IsEnum,
  IsOptional,
  // ValidateIf,
  IsString,
  IsNumber,
} from 'class-validator';
import validateConfig from '../utils/validate-config';

// enum FileDriver {
//   MEMORY = 'memory',
//   LOCAL = 'local',
//   S3 = 's3',
// }

class EnvVarValidator {
  // @IsEnum(FileDriver)
  // FILE_DRIVER: FileDriver;

  // @ValidateIf((value: EnvVarValidator) => value.FILE_DRIVER === FileDriver.S3)
  @IsString()
  ACCESS_KEY_ID: string;

  // @ValidateIf((value: EnvVarValidator) => value.FILE_DRIVER === FileDriver.S3)
  @IsString()
  SECRET_ACCESS_KEY: string;

  // @ValidateIf((value: EnvVarValidator) => value.FILE_DRIVER === FileDriver.S3)
  @IsString()
  AWS_DEFAULT_S3_BUCKET: string;

  // @ValidateIf((value: EnvVarValidator) => value.FILE_DRIVER === FileDriver.S3)
  // @IsString()
  // @IsOptional()
  // AWS_DEFAULT_S3_URL: string;

  // @ValidateIf((value: EnvVarValidator) => value.FILE_DRIVER === FileDriver.S3)
  @IsString()
  AWS_S3_REGION: string;

  @IsString()
  @IsOptional()
  AWS_S3_ENDPOINT: string;

  @IsNumber()
  AWS_S3_MAX_FILE_SIZE: number;

  // @ValidateIf(
  //   (value: EnvVarValidator) => value.FILE_DRIVER === FileDriver.MEMORY,
  // )
  // @IsString()
  // PINATA_API_KEY: string;

  // @ValidateIf(
  //   (value: EnvVarValidator) => value.FILE_DRIVER === FileDriver.MEMORY,
  // )
  // @IsString()
  // PINATA_SECRET_API_KEY: string;

  // @ValidateIf(
  //   (value: EnvVarValidator) => value.FILE_DRIVER === FileDriver.MEMORY,
  // )
  // @IsString()
  // PINATA_JWT_KEY: string;
}

export default registerAs<FileConfig>('file', () => {
  validateConfig(process.env, EnvVarValidator);

  return {
    // driver: process.env.FILE_DRIVER ?? 'local',
    accessKeyId: process.env.ACCESS_KEY_ID,
    secretAccessKey: process.env.SECRET_ACCESS_KEY,
    awsDefaultS3Bucket: process.env.AWS_DEFAULT_S3_BUCKET,
    // awsDefaultS3Url: process.env.AWS_DEFAULT_S3_URL,
    awsS3Region: process.env.AWS_S3_REGION,
    awsS3Endpoint: process.env.AWS_S3_ENDPOINT,
    maxFileSize: process.env.AWS_S3_MAX_FILE_SIZE
      ? parseInt(process.env.AWS_S3_MAX_FILE_SIZE, 10)
      : undefined,
    // pinataApiKey: process.env.PINATA_API_KEY,
    // pinataSecretApiKey: process.env.PINATA_SECRET_API_KEY,
    // pinataJWTKey: process.env.PINATA_JWT_KEY,
  };
});
