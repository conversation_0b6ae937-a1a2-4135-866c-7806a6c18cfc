import { HttpStatus } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';

export class CustomResponse {
  version: string;
  code: number;
  success: boolean;
  message: string | string[];
  data: any;
  totalRow: number;
  response?: any;
  status?: number;
  name?: string;

  constructor(
    code: number,
    message: string | string[],
    data: any,
    totalRow: number,
    success = false,
  ) {
    this.version = '0.0.1';
    this.code = code;
    this.message = message;
    if (typeof totalRow === 'number' && totalRow > 0) {
      this.totalRow = totalRow;
    }
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    this.data = data;
    this.success = success;
  }
}

export class HTTP {
  response: CustomResponse;

  setHttpRequest(
    code: number,
    message: string | string[],
    data: any = null,
    totalRow = 0,
    success = true,
  ) {
    if (code !== Number(HttpStatus.OK)) {
      success = false;
    }
    this.response = new CustomResponse(code, message, data, totalRow, success);
    return this.response;
  }

  success<T>(
    data: T | null = null,
    code = HttpStatus.OK,
    message = 'success',
    success = true,
    totalRow = 0,
  ) {
    this.response = new CustomResponse(code, message, data, totalRow, success);
    return this.response;
  }

  notFound() {
    this.response = new CustomResponse(
      HttpStatus.NOT_FOUND,
      'Not found',
      null,
      0,
      false,
    );
    return this.response;
  }

  error(
    message: string | string[],
    code: number = HttpStatus.BAD_REQUEST,
    error = '',
  ) {
    const response = new CustomResponse(code, message, null, 0, false);
    throw new RpcException(response);
  }
}
