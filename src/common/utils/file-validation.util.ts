import { ALLOWED_FILE_TYPES } from '@constants/file-types.constant';

/**
 * Validates if a file extension is allowed
 * @param filename - The filename to validate
 * @returns boolean indicating if the extension is allowed
 */
export const isValidFileExtension = (filename: string): boolean => {
  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
  const allowedExtensions = Object.values(ALLOWED_FILE_TYPES).flatMap(
    (type) => type.extensions,
  );
  return allowedExtensions.includes(extension as any);
};

/**
 * Validates if a MIME type is allowed
 * @param mimeType - The MIME type to validate
 * @returns boolean indicating if the MIME type is allowed
 */
export const isValidMimeType = (mimeType: string): boolean => {
  const allowedMimeTypes = Object.values(ALLOWED_FILE_TYPES).flatMap(
    (type) => type.mimeTypes,
  );
  return allowedMimeTypes.includes(mimeType.toLowerCase() as any);
};

/**
 * Gets the MIME type from a file extension
 * @param filename - The filename to extract extension from
 * @returns The MIME type or null if not found
 */
export const getMimeTypeFromExtension = (filename: string): string | null => {
  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));

  for (const config of Object.values(ALLOWED_FILE_TYPES)) {
    if ((config.extensions as readonly string[]).includes(extension)) {
      return config.mimeTypes[0];
    }
  }

  return null;
};

/**
 * Validates if a file URL has a valid format
 * @param url - The file URL to validate
 * @returns boolean indicating if the URL format is valid
 */
export const isValidFileUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    // Basic validation for S3 URLs or similar cloud storage URLs
    return (
      (urlObj.protocol === 'https:' || urlObj.protocol === 'http:') &&
      urlObj.hostname.length > 0 &&
      urlObj.pathname.length > 1
    );
  } catch {
    return false;
  }
};
