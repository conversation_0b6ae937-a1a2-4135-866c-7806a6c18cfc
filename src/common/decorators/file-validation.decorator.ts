import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';
import { isValidFileExtension } from '@common/utils';

export function IsValidFileExtension(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isValidFileExtension',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (typeof value !== 'string') return false;
          // Ensure extension starts with dot for validation
          const extension = value.startsWith('.') ? value : `.${value}`;
          const tempFileName = `temp${extension}`;
          return isValidFileExtension(tempFileName);
        },
        defaultMessage(args: ValidationArguments) {
          return 'Invalid file extension. Allowed extensions: PDF, DOCX, PPTX, XLSX, CSV, PNG, JPG, TXT';
        },
      },
    });
  };
}
