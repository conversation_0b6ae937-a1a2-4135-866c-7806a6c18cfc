import { Module } from '@nestjs/common';
import { ThreadGateway } from './thread.gateway';
import { ThreadsModule } from '../modules/threads/threads.module';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [
    ThreadsModule,
    ConfigModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>(
          'AUTH_JWT_SECRET',
          '9d4dc9dc-77a6-4ad1-a856-60fa431b7e02',
        ),
        signOptions: {
          expiresIn: configService.get<string>(
            'AUTH_JWT_TOKEN_EXPIRES_IN',
            '24h',
          ),
        },
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [
    ThreadGateway,
  ],
})
export class WebsocketModule {}