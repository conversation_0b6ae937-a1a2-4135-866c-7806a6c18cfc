import { Server, Socket } from 'socket.io';
import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ThreadsService } from '../modules/threads/threads.service';
// import { CustomLogger } from '../common/logging';
import { 
  WebSocketGateway, 
  WebSocketServer, 
  SubscribeMessage, 
  OnGatewayConnection, 
  OnGatewayDisconnect,
  OnGatewayInit
} from '@nestjs/websockets';

type JoinThreadRequest = {
  threadId: string;
  userId: string;
};

type SendMessageRequest = {
  threadId: string;
  userId: string;
  message: {
    status: string;
    message: string;
    timestamp: string;
  };
};

interface AuthenticatedSocket extends Socket {
  user?: {
    id: string;
    email: string;
    name: string;
  };
}

interface JwtPayload {
  sub: string;
  email: string;
  name: string;
  iat?: number;
  exp?: number;
}

@WebSocketGateway({
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  },
  namespace: '/threads'
})
@Injectable()
export class ThreadGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  constructor(
    private readonly jwtService: JwtService,
    // private readonly logger: CustomLogger,
    private readonly threadsService: ThreadsService
  ) {
    console.log('[ThreadGateway] threadsService injected:', !!this.threadsService);
  }

  afterInit(server: Server) {
    console.log('WebSocket Gateway initialized');
  }

  // JWT Authentication middleware
  private async authenticateSocket(
    socket: AuthenticatedSocket,
  ): Promise<boolean> {
    try {
      // Get token from handshake auth, headers, or query
      const token =
        socket.handshake.auth.token ||
        socket.handshake.headers.authorization?.replace('Bearer ', '') ||
        (socket.handshake.query.token as string);

      if (!token) {
        await socket.emit(
          'error',
          JSON.stringify({
            type: 'AuthenticationError',
            message: 'No token provided',
          }),
        );
        return false;
      }

      // Verify JWT token
      const payload = (await this.jwtService.verifyAsync(token)) as JwtPayload;

      // Attach user info to socket
      socket.user = {
        id: payload.sub,
        email: payload.email,
        name: payload.name,
      };

      console.log(
        `WebSocket authenticated: ${socket.user.name} (${socket.user.id})`,
      );
      return true;
    } catch (authError) {
      await socket.emit(
        'error',
        JSON.stringify({
          type: 'AuthenticationError',
          message: 'Invalid token',
        }),
      );
      return false;
    }
  }

  async handleConnection(socket: AuthenticatedSocket) {
    console.log(`Client attempting to connect: ${socket.id}`);

    // Authenticate the socket
    // TODO enable if the login function done 
    // const isAuthenticated = await this.authenticateSocket(socket);
    // if (!isAuthenticated) {
    //   console.log(`Authentication failed for socket: ${socket.id}`);
    //   socket.disconnect();
    //   return;
    // }

    console.log(`Client connected: ${socket.id} - User: ${socket.user?.name}`);
  }

  async handleDisconnect(socket: AuthenticatedSocket) {
    console.log(`Client disconnected: ${socket.id} - User: ${socket.user?.name}`);
  }

  @SubscribeMessage('joinThread')
  async handleJoinThread(socket: AuthenticatedSocket, data: any) {
    try {
      // if (!socket.user) {
      //   await socket.emit('error', 'User not authenticated');
      //   return;
      // }

      const request = data as JoinThreadRequest;
      const result = await this.threadsService.handleJoinThread(this.server, socket, request);
      
      // if (result.success) {
      //   await socket.emit('joinedThread', {
      //     threadId: request.threadId,
      //     message: 'Successfully joined thread'
      //   });
      // } else {
      //   await socket.emit('error', result.error);
      // }
    } catch (error) {
      console.error('Error in joinThread:', error);
      await socket.emit('error', 'Failed to join thread');
    }
  }

  @SubscribeMessage('sendMessage')
  async handleSendMessage(socket: AuthenticatedSocket, data: any) {
    try {
      // if (!socket.user) {
      //   await socket.emit('error', 'User not authenticated');
      //   return;
      // }

      const request = data as SendMessageRequest;
      const result = await this.threadsService.handleSendMessage(this.server, socket, request);
      
      // if (result.success) {
      //   await socket.emit('messageSent', {
      //     threadId: request.threadId,
      //     message: 'Message sent successfully'
      //   });
      // } else {
      //   await socket.emit('error', result.error);
      // }
    } catch (error) {
      console.error('Error in sendMessage:', error);
      await socket.emit('error', 'Failed to send message');
    }
  }

  @SubscribeMessage('leaveThread')
  async handleLeaveThread(socket: AuthenticatedSocket, data: any) {
    try {
      if (!socket.user) {
        await socket.emit('error', 'User not authenticated');
        return;
      }

      const { threadId } = data;
      await socket.leave(threadId);
      await socket.emit('leftThread', {
        threadId,
        message: 'Successfully left thread'
      });
      
      console.log(`User ${socket.user.name} left thread: ${threadId}`);
    } catch (error) {
      console.error('Error in leaveThread:', error);
      await socket.emit('error', 'Failed to leave thread');
    }
  }

  @SubscribeMessage('typing')
  async handleTyping(socket: AuthenticatedSocket, data: any) {
    try {
      if (!socket.user) {
        return;
      }

      const { threadId, isTyping } = data;
      socket.to(threadId).emit('userTyping', {
        userId: socket.user.id,
        userName: socket.user.name,
        isTyping
      });
    } catch (error) {
      console.error('Error in typing:', error);
    }
  }

  @SubscribeMessage('markAsRead')
  async handleMarkAsRead(socket: AuthenticatedSocket, data: any) {
    try {
      if (!socket.user) {
        return;
      }

      const { threadId, messageId } = data;
      socket.to(threadId).emit('messageRead', {
        userId: socket.user.id,
        userName: socket.user.name,
        messageId
      });
    } catch (error) {
      console.error('Error in markAsRead:', error);
    }
  }

  // Helper methods for broadcasting
  public broadcastToThread(
    threadId: string,
    event: string,
    data: any,
  ) {
    this.server.to(threadId).emit(event, data);
  }

  public sendToUser(userId: string, event: string, data: any) {
    this.server.to(userId).emit(event, data);
  }

  public getOnlineUsersInThread(threadId: string) {
    const room = this.server.sockets.adapter.rooms.get(threadId);
    return room ? Array.from(room) : [];
  }
}
