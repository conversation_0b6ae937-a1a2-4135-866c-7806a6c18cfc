# Technology Stack

## Framework & Runtime

- **NestJS**: TypeScript-first Node.js framework with decorators and dependency injection
- **Node.js**: v20.18.0 runtime environment
- **TypeScript**: Strongly typed JavaScript with ES2021 target

## Databases

- **PostgreSQL**: Primary relational database for users, courses, assignments, and documents
- **MongoDB**: Document database for threads and real-time messaging
- **Redis**: Caching and session management

## Key Libraries & Tools

- **TypeORM**: PostgreSQL ORM with migrations and entity management
- **Mongoose**: MongoDB ODM for thread/message collections
- **Passport JWT**: Authentication strategy implementation
- **Socket.IO**: WebSocket implementation for real-time features
- **AWS SDK**: S3 integration for file storage
- **Winston**: Structured logging with daily rotation
- **Class Validator**: DTO validation with decorators
- **Swagger/OpenAPI**: API documentation generation

## Development Tools

- **ESLint + Prettier**: Code formatting and linting
- **<PERSON>sky**: Git hooks for pre-commit validation
- **Jest**: Unit and e2e testing framework
- **Docker**: Containerization for development and production

## Common Commands

### Development

```bash
# Start development server with hot reload
npm run start:dev

# Run tests
npm run test
npm run test:e2e
npm run test:cov

# Code quality
npm run lint
npm run format
```

### Database Management

```bash
# PostgreSQL migrations
npm run migration:generate
npm run migration:run
npm run migration:revert

# MongoDB migrations
npm run mongodb:migrate up
npm run mongodb:migrate down

# Seed data
npm run seed:run
```

### Build & Deploy

```bash
# Build for production
npm run build

# Start production server
npm run start:prod

# Docker deployment
docker compose up -d
```

## Architecture Patterns

- **Module-based architecture**: Feature modules with controllers, services, and entities
- **Dependency injection**: NestJS IoC container for service management
- **Repository pattern**: TypeORM repositories for data access
- **Guard-based authorization**: JWT guards with role-based permissions
- **Middleware pipeline**: Request context, logging, and validation
- **Event-driven**: WebSocket gateways for real-time communication
