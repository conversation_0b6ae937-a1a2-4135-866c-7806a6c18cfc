# Project Structure

## Root Directory Organization

```
├── src/                    # Source code
├── test/                   # E2E tests
├── dist/                   # Compiled output
├── node_modules/           # Dependencies
├── .cursor/                # Cursor IDE configuration and PRD documents
├── .kiro/                  # Kiro steering rules
└── sit-be/logs/           # Application logs
```

## Source Code Structure (`src/`)

### Core Application

- `main.ts` - Application bootstrap with Swagger, CORS, and global configuration
- `app.module.ts` - Root module with database connections, guards, and middleware
- `app.controller.ts` & `app.service.ts` - Basic application endpoints

### Common Utilities (`src/common/`)

- `base/entities/` - Base entity with UUID, timestamps, and soft delete
- `configs/` - Configuration modules for database, auth, AWS, mail, etc.
- `database/` - TypeORM and MongoDB setup, migrations, and seeds
- `decorators/` - Custom decorators for validation and permissions
- `filters/` - Exception filters for error handling
- `guards/` - JWT authentication and permission guards
- `i18n/` - Internationalization files (English/Vietnamese)
- `middlewares/` - Request context and logging middleware
- `permissions/` - Role-based permission definitions
- `utils/` - Utility functions for validation, dates, files, etc.

### Feature Modules (`src/modules/`)

Each module follows the standard NestJS pattern:

```
module-name/
├── entities/              # TypeORM entities
├── dto/                   # Data Transfer Objects
├── module-name.controller.ts
├── module-name.service.ts
└── module-name.module.ts
```

**Key Modules:**

- `auth/` - Authentication, JWT strategy, ADFS integration
- `users/` - User management with admin and regular user controllers
- `courses/` - Course entity and relationships
- `assignments/` - Assignment management
- `assignment-groups/` - Group organization for assignments
- `documents/` - File management and sharing
- `threads/` - Real-time messaging (MongoDB-based)
- `s3/` - AWS S3 file upload and presigned URLs
- `mail/` - Email templates and sending service

### Supporting Services

- `health/` - Health check endpoints
- `websocket/` - Socket.IO gateway for real-time features

## Database Structure

### PostgreSQL Entities

- Users (with roles: admin, instructor, student)
- Courses and CourseUsers (enrollment)
- Assignments and AssignmentGroups
- GroupUsers (group membership)
- Documents (file metadata and permissions)

### MongoDB Collections

- Threads (conversation containers)
- Messages (real-time chat messages)

## Configuration Patterns

### Path Aliases (tsconfig.json)

```typescript
"@src/*": ["src/*"]
"@common/*": ["src/common/*"]
"@modules/*": ["src/modules/*"]
"@auth/*": ["src/auth/*"]
// ... other aliases for major directories
```

### Environment Configuration

- `.env` - Environment variables
- `src/common/configs/` - Typed configuration modules
- Validation using class-validator decorators

### File Naming Conventions

- **Entities**: `entity-name.entity.ts`
- **DTOs**: `action-entity.dto.ts` (e.g., `create-user.dto.ts`)
- **Controllers**: `module-name.controller.ts`
- **Services**: `module-name.service.ts`
- **Modules**: `module-name.module.ts`
- **Migrations**: `timestamp-DescriptiveName.ts`

## Development Workflow

1. Feature modules are self-contained with their own entities, DTOs, and business logic
2. Common functionality is centralized in `src/common/`
3. Database changes require migrations for both PostgreSQL and MongoDB
4. All endpoints are documented with Swagger decorators
5. Role-based permissions are enforced through guards and decorators
