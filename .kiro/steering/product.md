# Product Overview

SIT API is a group assignment management system designed for educational institutions. The application facilitates collaboration between instructors and students through:

## Core Features

- **User Management**: Role-based access control for administrators, instructors, and students
- **Course & Assignment Management**: Hierarchical organization of courses, assignments, and groups
- **Document Management**: File upload, sharing, and organization within assignment contexts
- **Real-time Communication**: WebSocket-based chat functionality for group collaboration
- **Authentication**: JWT-based authentication with ADFS/OAuth integration support

## User Roles

- **Administrators**: System-wide management and oversight
- **Instructors**: Course and assignment creation, student management
- **Students**: Assignment participation, group collaboration, document sharing

## Key Workflows

1. Instructors create courses and assignments
2. Students are organized into assignment groups
3. Groups collaborate through shared documents and real-time chat
4. File management supports both assignment-level and group-specific contexts

The system emphasizes secure, role-based access to educational resources with real-time collaboration capabilities.
