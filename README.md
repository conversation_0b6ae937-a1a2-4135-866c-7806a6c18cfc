<p align="center">
  <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo-small.svg" width="120" alt="Nest Logo" /></a>
</p>

[circleci-image]: https://img.shields.io/circleci/build/github/nestjs/nest/master?token=abc123def456
[circleci-url]: https://circleci.com/gh/nestjs/nest

  <p align="center">A progressive <a href="http://nodejs.org" target="_blank">Node.js</a> framework for building efficient and scalable server-side applications.</p>
    <p align="center">
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/v/@nestjs/core.svg" alt="NPM Version" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/l/@nestjs/core.svg" alt="Package License" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/dm/@nestjs/common.svg" alt="NPM Downloads" /></a>
<a href="https://circleci.com/gh/nestjs/nest" target="_blank"><img src="https://img.shields.io/circleci/build/github/nestjs/nest/master" alt="CircleCI" /></a>
<a href="https://coveralls.io/github/nestjs/nest?branch=master" target="_blank"><img src="https://coveralls.io/repos/github/nestjs/nest/badge.svg?branch=master#9" alt="Coverage" /></a>
<a href="https://discord.gg/G7Qnnhy" target="_blank"><img src="https://img.shields.io/badge/discord-online-brightgreen.svg" alt="Discord"/></a>
<a href="https://opencollective.com/nest#backer" target="_blank"><img src="https://opencollective.com/nest/backers/badge.svg" alt="Backers on Open Collective" /></a>
<a href="https://opencollective.com/nest#sponsor" target="_blank"><img src="https://opencollective.com/nest/sponsors/badge.svg" alt="Sponsors on Open Collective" /></a>
  <a href="https://paypal.me/kamilmysliwiec" target="_blank"><img src="https://img.shields.io/badge/Donate-PayPal-ff3f59.svg" alt="Donate us"/></a>
    <a href="https://opencollective.com/nest#sponsor"  target="_blank"><img src="https://img.shields.io/badge/Support%20us-Open%20Collective-41B883.svg" alt="Support us"></a>
  <a href="https://twitter.com/nestframework" target="_blank"><img src="https://img.shields.io/twitter/follow/nestframework.svg?style=social&label=Follow" alt="Follow us on Twitter"></a>
</p>
  <!--[![Backers on Open Collective](https://opencollective.com/nest/backers/badge.svg)](https://opencollective.com/nest#backer)
  [![Sponsors on Open Collective](https://opencollective.com/nest/sponsors/badge.svg)](https://opencollective.com/nest#sponsor)-->

## Description

[Nest](https://github.com/nestjs/nest) framework TypeScript starter repository.

## Project setup

```bash
$ npm install
```

## Database Setup

This project uses PostgreSQL for relational data and MongoDB for threads/messages. Follow this **exact sequence** to set up the databases completely:

### Complete Setup Flow

#### Step 1: Create PostgreSQL Database + Run Migrations

```bash
# Start PostgreSQL using Docker Compose
docker compose up -d db

# Create database tables and relationships
npm run migration:run
```

#### Step 2: Create MongoDB Thread Database + Run Migrations

```bash
# Start MongoDB using Docker Compose
docker compose up -d thread-db

# Run MongoDB migrations to set up collections and indexes
npm run mongodb:migrate up
```

#### Step 3: Run Seeding

```bash
# Seed both PostgreSQL and MongoDB with test data
# This will automatically seed PostgreSQL first, then MongoDB
npm run seed:run
```

### Quick Setup Commands

```bash
# Complete setup in order:
docker compose up -d db thread-db
npm run migration:run
npm run mongodb:migrate up
npm run seed:run
```

### Prerequisites

1. **Docker Compose**: Ensure Docker is running for database containers
2. **Environment Variables**: Configure your `.env` file with database connection details

### Database Commands Reference

**PostgreSQL Commands:**

```bash
# Run all pending migrations
npm run migration:run

# Revert the last migration
npm run migration:revert

# Drop all tables (⚠️ destructive)
npm run schema:drop
```

**MongoDB Commands:**

```bash
# Run MongoDB migrations
npm run mongodb:migrate up

# Check MongoDB migration status
npm run mongodb:migrate status
```

**Seeding Commands:**

```bash
# Seed both databases (PostgreSQL → MongoDB)
npm run seed:run

```

For more details on MongoDB migrations, see the [MongoDB Migration System](#mongodb-migration-system) section below.

## Compile and run the project

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

## Run tests

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

## MongoDB Migration System

The MongoDB migration system helps manage database schema changes and initialization for the threads and messages collections.

### Available Commands

```bash
# Run all pending migrations
npm run mongodb:migrate up

# Rollback the last migration
npm run mongodb:migrate down

# Rollback multiple migrations
npm run mongodb:migrate down 3

# Check migration status
npm run mongodb:migrate status
```

### Creating New Migrations

Use the automated migration generator to create new migrations:

```bash
# Create a migration with a descriptive name
npm run mongodb:migration:create create-posts-collection
```

This command will:

1. ✅ Generate a new migration file with proper versioning (e.g., `002-add-user-indexes.migration.ts`)
2. ✅ Automatically register the migration in `migration.service.ts`
3. ✅ Create the migration template with `up()` and `down()` methods

After running the command, you only need to:

1. Edit the generated migration file to add your migration logic
2. The registration is handled automatically

#### Manual Migration Creation (Alternative)

If you prefer to create migrations manually:

1. Create a new migration file in `src/common/database/mongodb/migrations/`
2. Follow the naming convention: `XXX-description.migration.ts`
3. Implement the `IMigration` interface:

```typescript
import { Connection } from 'mongoose';
import { IMigration } from '../migration.interface';

export class MyNewMigration implements IMigration {
  name = 'MyNewMigration';
  version = '002';

  async up(connection: Connection): Promise<void> {
    // Migration logic here
  }

  async down(connection: Connection): Promise<void> {
    // Rollback logic here
  }
}
```

4. Register the migration in `migration.service.ts`

### Initial Setup

1. Start the MongoDB container:

   ```bash
   docker-compose up -d thread-db
   ```

2. Run the initial migrations:

   ```bash
   npm run mongodb:migrate up
   ```

3. Verify the setup:
   ```bash
   npm run mongodb:migrate status
   ```

The database is now ready for use with the threads application!

## Deployment

When you're ready to deploy your NestJS application to production, there are some key steps you can take to ensure it runs as efficiently as possible. Check out the [deployment documentation](https://docs.nestjs.com/deployment) for more information.

If you are looking for a cloud-based platform to deploy your NestJS application, check out [Mau](https://mau.nestjs.com), our official platform for deploying NestJS applications on AWS. Mau makes deployment straightforward and fast, requiring just a few simple steps:

```bash
$ npm install -g mau
$ mau deploy
```

With Mau, you can deploy your application in just a few clicks, allowing you to focus on building features rather than managing infrastructure.

## Resources

Check out a few resources that may come in handy when working with NestJS:

- Visit the [NestJS Documentation](https://docs.nestjs.com) to learn more about the framework.
- For questions and support, please visit our [Discord channel](https://discord.gg/G7Qnnhy).
- To dive deeper and get more hands-on experience, check out our official video [courses](https://courses.nestjs.com/).
- Deploy your application to AWS with the help of [NestJS Mau](https://mau.nestjs.com) in just a few clicks.
- Visualize your application graph and interact with the NestJS application in real-time using [NestJS Devtools](https://devtools.nestjs.com).
- Need help with your project (part-time to full-time)? Check out our official [enterprise support](https://enterprise.nestjs.com).
- To stay in the loop and get updates, follow us on [X](https://x.com/nestframework) and [LinkedIn](https://linkedin.com/company/nestjs).
- Looking for a job, or have a job to offer? Check out our official [Jobs board](https://jobs.nestjs.com).

## Support

Nest is an MIT-licensed open source project. It can grow thanks to the sponsors and support by the amazing backers. If you'd like to join them, please [read more here](https://docs.nestjs.com/support).

## Stay in touch

- Author - [Kamil Myśliwiec](https://twitter.com/kammysliwiec)
- Website - [https://nestjs.com](https://nestjs.com/)
- Twitter - [@nestframework](https://twitter.com/nestframework)

## License

Nest is [MIT licensed](https://github.com/nestjs/nest/blob/master/LICENSE).
