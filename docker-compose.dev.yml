version: '3.8'
services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    ports:
      - '${PORT_EXPOSE}:${APP_PORT}'
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
    depends_on:
      - db
      - thread-db
      - redis
    networks:
      - app-network
    container_name: sit-api
    restart: unless-stopped
  db:
    image: postgres:16
    environment:
      POSTGRES_USER: ${DATABASE_USERNAME}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
      POSTGRES_DB: ${DATABASE_NAME}
    ports:
      - '${POSTGRES_EXPOSE_PORT}:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network
    container_name: sit-db
  thread-db:
    image: mongo:4.4
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_INITDB_ROOT_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_INITDB_ROOT_PASSWORD}
      MONGO_INITDB_DATABASE: ${MONGO_INITDB_DATABASE}
    ports:
      - '${MONGODB_EXPOSE_PORT}:27017'
    volumes:
      - mongodb_data:/data/db
    networks:
      - app-network
    container_name: sit-mongodb
    restart: unless-stopped
  redis:
    image: redis:7-alpine
    ports:
      - '${REDIS_EXPOSE_PORT}:6379'
    networks:
      - app-network
    container_name: sit-redis
    restart: unless-stopped
volumes:
  postgres_data:
  mongodb_data:
networks:
  app-network:
    driver: bridge
