FROM node:20.18.0-alpine AS builder
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache python3 make g++

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy source code and build
COPY . .
RUN npm run build

# Production stage
FROM node:20.18.0-alpine AS production
WORKDIR /app

# Install runtime dependencies
RUN apk add --no-cache tini

# Copy package files and install production dependencies
COPY package*.json ./
RUN npm ci --only=production --ignore-scripts && npm cache clean --force

# Copy built application
COPY --from=builder /app/dist ./dist

# Copy node_modules with compiled native modules from builder stage
COPY --from=builder /app/node_modules ./node_modules

# Create logs directory (as root, no permission issues)
RUN mkdir -p /app/logs

# Use tini as entrypoint
ENTRYPOINT ["/sbin/tini", "--"]

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3000/api/v1/health || exit 1

# Start application
CMD ["node", "dist/main"]
