stages:
  - build-develop
  - deploy-develop

variables:
  DOCKER_HOST: tcp://docker:2375
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  DOCKER_BUILDKIT: 1
  IMAGE_NAME: "$CI_REGISTRY_IMAGE/api"
  IMAGE_TAG: "$CI_COMMIT_SHA"
  CACHE_IMAGE: "$CI_REGISTRY_IMAGE/api:cache"

# Build stage for develop branch
build_develop:
  stage: build-develop
  image: docker:latest
  services:
    - docker:dind
  cache:
    key: docker-cache-$CI_COMMIT_REF_SLUG
    paths:
      - .docker-cache/
  before_script:
    - echo -n $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
    - mkdir -p .docker-cache
  script:
    # Pull previous cache image if exists
    - docker pull $CACHE_IMAGE || true
    # Build with cache from previous builds and registry
    - |
      docker build \
        --target production \
        --cache-from $CACHE_IMAGE \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        -t $IMAGE_NAME:$IMAGE_TAG \
        -t $CACHE_IMAGE .
    # Push both the tagged image and cache
    - docker push $IMAGE_NAME:$IMAGE_TAG
    - docker push $CACHE_IMAGE
  only:
    - develop

# Deploy stage for develop branch
deploy_develop:
  stage: deploy-develop
  image: alpine:3.18
  cache:
    key: ssh-cache
    paths:
      - .ssh-cache/
  before_script:
    - apk add --no-cache openssh-client git
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh .ssh-cache
    - chmod 700 ~/.ssh
    # Use cached known_hosts if available
    - cp .ssh-cache/known_hosts ~/.ssh/known_hosts 2>/dev/null || ssh-keyscan -p 2222 $DEV_SERVER_HOST >> ~/.ssh/known_hosts
    - cp ~/.ssh/known_hosts .ssh-cache/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - |
      ssh -o StrictHostKeyChecking=no -p 2222 $DEV_SERVER_USER@$DEV_SERVER_HOST << EOF
        set -e
        cd $DEV_PROJECT_PATH
        
        # Git pull with error handling
        git pull origin develop
        
        # Docker operations with better error handling
        echo -n $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
        
        # Pull new image
        docker pull $IMAGE_NAME:$IMAGE_TAG
        
        # Stop existing container gracefully
        docker compose -f compose.yml stop api || true
        
        # Tag and deploy
        docker tag $IMAGE_NAME:$IMAGE_TAG sit-api:latest
        docker compose -f compose.yml up -d --no-deps api
        
        # Cleanup old images (keep last 3)
        docker images $IMAGE_NAME --format "table {{.Tag}}" | tail -n +4 | xargs -r docker rmi $IMAGE_NAME: 2>/dev/null || true
        docker image prune -f --filter "until=24h"
      EOF
  only:
    - develop
  dependencies:
    - build_develop