# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NODE_ENV=development
APP_PORT=44350
PORT_EXPOSE=8006
APP_NAME="SIT API"
API_PREFIX=api
APP_FALLBACK_LANGUAGE=en
APP_HEADER_LANGUAGE=x-custom-lang

# =============================================================================
# DATABASE CONFIGURATION (PostgreSQL)
# =============================================================================
DATABASE_TYPE=postgres
# Database URL using variables above (for docker-compose with service name 'db')
DATABASE_HOST=db
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=123456
DATABASE_NAME=sit-dev
DATABASE_MAX_CONNECTIONS=2
POSTGRES_EXPOSE_PORT=8003

DATABASE_URL=postgresql://${DATABASE_USERNAME}:${DATABASE_PASSWORD}@${DATABASE_HOST}:${DATABASE_PORT}/${DATABASE_NAME}

# =============================================================================
# MONGODB CONFIGURATION
# =============================================================================
# MongoDB Docker configuration
MONGO_INITDB_ROOT_USERNAME=mongodb_user
MONGO_INITDB_ROOT_PASSWORD=123456
MONGO_INITDB_DATABASE=sit_thread
MONGODB_EXPOSE_PORT=8004

# MongoDB connection URI using variables above
MONGODB_URI=mongodb://${MONGO_INITDB_ROOT_USERNAME}:${MONGO_INITDB_ROOT_PASSWORD}@thread-db:27017/${MONGO_INITDB_DATABASE}?authSource=admin

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
# Redis configuration for docker-compose (using service name 'redis')
API_REDIS_HOST=redis
API_REDIS_PORT=6379

# Redis configuration for local development
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_EXPOSE_PORT=8005

# =============================================================================
# AUTHENTICATION & JWT
# =============================================================================
AUTH_JWT_SECRET=9d4dc9dc-77a6-4ad1-a856-60fa431b7e02
AUTH_JWT_TOKEN_EXPIRES_IN=24h
AUTH_REFRESH_SECRET=secret_for_refresh
AUTH_REFRESH_TOKEN_EXPIRES_IN=3650d

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=debug
LOG_CONSOLE_ENABLED=true
LOG_FILE_ENABLED=true
LOG_DIR=/app/logs
LOG_FILE_NAME_PATTERN=sit-app-%DATE%.log
LOG_FILE_MAX_SIZE=10m
LOG_FILE_RETENTION=7d

# =============================================================================
# EMAIL/MAIL CONFIGURATION
# =============================================================================
MAIL_HOST=127.0.0.1
MAIL_PORT=1025
MAIL_USER=alo
MAIL_PASSWORD=blo
MAIL_IGNORE_TLS=true
MAIL_SECURE=false
MAIL_REQUIRE_TLS=false
MAIL_DEFAULT_EMAIL=<EMAIL>
MAIL_DEFAULT_NAME=Api
MAIL_CLIENT_PORT=1080

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================
FILE_DRIVER=local
ACCESS_KEY_ID=
SECRET_ACCESS_KEY=
AWS_S3_REGION=ap-southeast-1
AWS_DEFAULT_S3_BUCKET=sit-chatbot-develop
AWS_S3_ENDPOINT=
AWS_S3_MAX_FILE_SIZE=104857600
PINATA_API_KEY=
PINATA_SECRET_API_KEY=
PINATA_JWT_KEY=

# =============================================================================
# ADFS/OAUTH CONFIGURATION
# =============================================================================
ADFS_RELYING_PARTY_ID=
ADFS_CLIENT_ID=
ADFS_SERVER=
ADFS_AUDIENCE=
ADFS_CALLBACK_URL=
ADFS_AUTHORIZATION_URL=
ADFS_TOKEN_URL=
ADFS_FE_CALLBACK_SUCCESS=
ADFS_FE_CALLBACK_ERROR=

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
NOTIFICATION_ADMIN_EMAILS=<EMAIL>
CSKH_EMAIL=<EMAIL>

# =============================================================================
# MISCELLANEOUS
# =============================================================================
PRICE_TEMPLATE=
